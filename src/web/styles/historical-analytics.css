/**
 * CodePulse Historical Analytics Dashboard Stylesheet
 *
 * This stylesheet provides comprehensive styling for the historical data analysis
 * dashboard, enabling deep insights into long-term trends, patterns, and performance
 * regressions with advanced visualization and analytical capabilities. Key features include:
 *
 * version 1.0.0
 *
 * - Long-term trend analysis with interactive charts
 * - Pattern recognition and confidence scoring
 * - Performance regression detection and alerts
 * - Comparative analysis across time periods
 * - Health scoring and key metrics tracking
 * - Advanced filtering and data exploration
 * - Predictive analytics and forecasting
 * - Responsive design for all device sizes
 * - Accessibility-compliant interface elements
 * - Professional analytical styling with smooth interactions
 */

/* ======================================================================= */
/* MAIN CONTAINER LAYOUT                                                   */
/* ======================================================================= */

/**
 * Primary Container
 * Main wrapper for the historical analytics dashboard with consistent
 * styling and layout structure for comprehensive data analysis interface.
 */

/* Main historical analytics container */
.historical-analytics-container {
  padding: 1.5rem;
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  margin-bottom: 2rem;
}

/* Analytics dashboard header */
.analytics-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border-primary);
}

/* Header title styling */
.analytics-header h2 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

/* Analytics control buttons container */
.analytics-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Analytics dropdown selectors */
.analytics-select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

/* Analytics select focus state */
.analytics-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* ======================================================================= */
/* ANALYTICS SUMMARY CARDS                                                 */
/* ======================================================================= */

/**
 * Summary Cards
 * High-level overview cards displaying key analytics metrics with
 * color-coded status indicators and hover effects for enhanced interaction.
 */

/* Summary cards grid layout */
.analytics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

/* Individual summary card */
.summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

/* Summary card hover effects */
.summary-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* Summary card icon */
.summary-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

/* Summary card content heading */
.summary-content h3 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Summary metric value */
.summary-value {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
}

/**
 * Summary Value Status Colors
 * Color-coded indicators for different metric states and performance levels.
 */

/* Trend indicators */
.summary-value.improving { color: var(--color-success); }
.summary-value.stable { color: var(--color-warning); }
.summary-value.declining { color: var(--color-danger); }

/* Severity levels */
.summary-value.low { color: var(--color-success); }
.summary-value.medium { color: var(--color-warning); }
.summary-value.high { color: var(--color-danger); }

/* Quality ratings */
.summary-value.excellent { color: var(--color-success); }
.summary-value.good { color: var(--color-info); }
.summary-value.fair { color: var(--color-warning); }
.summary-value.poor { color: var(--color-danger); }

/* ======================================================================= */
/* TAB NAVIGATION SYSTEM                                                    */
/* ======================================================================= */

/**
 * Analytics Tabs
 * Navigation interface for switching between different analytical views
 * including overview, trends, patterns, and regressions with active states.
 */

/* Tab navigation container */
.analytics-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border-primary);
  margin-bottom: 2rem;
  overflow-x: auto;
}

/* Individual analytics tab */
.analytics-tab {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  border-bottom: 2px solid transparent;
}

/* Tab hover state */
.analytics-tab:hover {
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
}

/* Active tab styling */
.analytics-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background: var(--color-primary-light);
}

/* Hidden tab content */
.analytics-tab-content {
  display: none;
}

/* Active tab content */
.analytics-tab-content.active {
  display: block;
}

/* ======================================================================= */
/* OVERVIEW TAB INTERFACE                                                  */
/* ======================================================================= */

/**
 * Overview Dashboard
 * Comprehensive overview layout with health scoring, key metrics,
 * and alert monitoring in a three-column responsive grid.
 */

/* Overview grid layout */
.overview-grid {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 2rem;
}

/* Overview section containers */
.overview-section {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
}

/* Overview section headings */
.overview-section h3 {
  margin: 0 0 1rem 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/**
 * Health Score Display
 * Visual health scoring system with detailed breakdown
 * and circular progress indicators.
 */

/* Health score container layout */
.health-score-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

/* Health score details section */
.health-score-details {
  flex: 1;
}

/* Individual health detail item */
.health-detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

/* Health detail labels */
.health-label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Health detail values */
.health-value {
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/**
 * Key Metrics Grid
 * Grid layout for displaying essential performance metrics
 * with trend indicators and change calculations.
 */

/* Key metrics grid layout */
.key-metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

/* Individual key metric card */
.key-metric-card {
  padding: 1rem;
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-base);
  text-align: center;
}

/* Key metric card headings */
.key-metric-card h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

/* Metric value display */
.metric-value {
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: 0.25rem;
}

/* Metric change indicator */
.metric-change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.25rem;
}

/* Positive and negative change colors */
.metric-change.positive { color: var(--color-success); }
.metric-change.negative { color: var(--color-danger); }

/* Metric trend description */
.metric-trend {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  text-transform: capitalize;
}

/**
 * Alerts System
 * Scrollable alert list with severity-based color coding
 * and comprehensive alert information display.
 */

/* Scrollable alerts list container */
.alerts-list {
  max-height: 300px;
  overflow-y: auto;
}

/* Individual alert item */
.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-base);
  border-left: 4px solid var(--color-border-secondary);
}

/* Alert severity indicators */
.alert-item.critical { border-left-color: var(--color-danger); }
.alert-item.major { border-left-color: var(--color-warning); }
.alert-item.minor { border-left-color: var(--color-info); }
.alert-item.warning { border-left-color: var(--color-primary); }

/* Alert icon styling */
.alert-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

/* Alert content container */
.alert-content {
  flex: 1;
  min-width: 0;
}

/* Alert title */
.alert-title {
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: 0.25rem;
}

/* Alert description */
.alert-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

/* Alert timestamp */
.alert-time {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
}

/* Empty alerts state */
.no-alerts {
  text-align: center;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-sm);
  padding: 2rem;
}

/* ======================================================================= */
/* TRENDS TAB INTERFACE                                                    */
/* ======================================================================= */

/**
 * Trends Analysis
 * Comprehensive trend analysis interface with filtering capabilities,
 * interactive charts, and statistical summaries for long-term data insights.
 */

/* Trends container */
.trends-container {
  margin-bottom: 1.5rem;
}

/* Trends filtering controls */
.trends-filters {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
}

/* Trends filter labels */
.trends-filters label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
}

/* Trends charts grid */
.trends-charts {
  display: grid;
  gap: 2rem;
}

/* Individual trend chart container */
.trend-chart-container {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
}

/* Trend chart header with title and stats */
.trend-chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

/* Trend chart title */
.trend-chart-title {
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

/* Trend chart statistics container */
.trend-chart-stats {
  display: flex;
  gap: 1rem;
  font-size: var(--font-size-sm);
}

/* Individual trend statistic */
.trend-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Trend statistic label */
.trend-stat-label {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  margin-bottom: 0.25rem;
}

/* Trend statistic value */
.trend-stat-value {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

/* ======================================================================= */
/* PATTERNS TAB INTERFACE                                                  */
/* ======================================================================= */

/**
 * Pattern Recognition
 * Advanced pattern detection and analysis interface with confidence scoring,
 * impact assessment, and actionable recommendations for identified patterns.
 */

/* Patterns grid layout */
.patterns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Individual pattern card */
.pattern-card {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  transition: all var(--transition-fast);
}

/* Pattern card hover effects */
.pattern-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* Pattern card header */
.pattern-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

/* Pattern title */
.pattern-title {
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

/**
 * Pattern Impact Indicators
 * Color-coded badges indicating the impact level of detected patterns.
 */

/* Base pattern impact badge */
.pattern-impact {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

/* High impact pattern */
.pattern-impact.high {
  background: rgba(239, 68, 68, 0.1);
  color: var(--color-danger);
}

/* Medium impact pattern */
.pattern-impact.medium {
  background: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
}

/* Low impact pattern */
.pattern-impact.low {
  background: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

/**
 * Pattern Details
 * Detailed pattern information including description, confidence scoring,
 * and actionable recommendations for pattern optimization.
 */

/* Pattern description text */
.pattern-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin-bottom: 1rem;
}

/* Pattern confidence display */
.pattern-confidence {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

/* Confidence label */
.pattern-confidence-label {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
}

/* Confidence progress bar */
.pattern-confidence-bar {
  flex: 1;
  height: 4px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

/* Animated confidence fill */
.pattern-confidence-fill {
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
}

/**
 * Pattern Recommendations
 * Bulleted list of actionable recommendations for addressing
 * identified patterns with custom bullet styling.
 */

/* Recommendations list container */
.pattern-recommendations {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Individual recommendation item */
.pattern-recommendations li {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

/* Custom bullet point */
.pattern-recommendations li::before {
  content: '•';
  color: var(--color-primary);
  position: absolute;
  left: 0;
}

/* ======================================================================= */
/* REGRESSIONS TAB INTERFACE                                               */
/* ======================================================================= */

/**
 * Performance Regression Detection
 * Advanced regression analysis interface with severity classification,
 * detailed metrics, and actionable remediation steps for performance issues.
 */

/* Regressions container */
.regressions-container {
  margin-bottom: 1.5rem;
}

/* Regressions filtering controls */
.regressions-filters {
  margin-bottom: 1.5rem;
}

/* Regressions list container */
.regressions-list {
  margin-bottom: 1rem;
}

/**
 * Regression Cards
 * Individual regression display cards with severity-based color coding
 * and comprehensive regression information.
 */

/* Base regression card */
.regression-card {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border-left: 4px solid var(--color-border-secondary);
}

/* Regression severity indicators */
.regression-card.critical { border-left-color: var(--color-danger); }
.regression-card.major { border-left-color: var(--color-warning); }
.regression-card.minor { border-left-color: var(--color-info); }
.regression-card.warning { border-left-color: var(--color-primary); }

/* Regression card header */
.regression-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

/* Regression title */
.regression-title {
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

/**
 * Regression Severity Badges
 * Color-coded severity indicators for different regression levels.
 */

/* Base severity badge */
.regression-severity {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

/* Critical severity */
.regression-severity.critical {
  background: rgba(239, 68, 68, 0.1);
  color: var(--color-danger);
}

/* Major severity */
.regression-severity.major {
  background: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
}

/* Minor severity */
.regression-severity.minor {
  background: rgba(59, 130, 246, 0.1);
  color: var(--color-info);
}

/* Warning severity */
.regression-severity.warning {
  background: rgba(37, 99, 235, 0.1);
  color: var(--color-primary);
}

/**
 * Regression Metrics
 * Performance metrics comparison grid showing before/after values
 * and impact assessment for detected regressions.
 */

/* Regression metrics grid layout */
.regression-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

/* Individual regression metric card */
.regression-metric {
  text-align: center;
}

/* Metric label */
.regression-metric-label {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  margin-bottom: 0.25rem;
}

/* Metric value */
.regression-metric-value {
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* Regression description */
.regression-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin-bottom: 1rem;
}

/* Regression action buttons container */
/**
 * Regression Actions
 * Action buttons for regression remediation and investigation
 * with hover effects and interactive styling.
 */

/* Regression actions container */
.regression-actions {
  display: flex;
  gap: 0.5rem;
}

/* Regression action button */
.regression-action {
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  background: var(--color-bg-primary);
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
}

/* Regression action button hover state */
.regression-action:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* ======================================================================= */
/* RESPONSIVE DESIGN                                                       */
/* ======================================================================= */

/**
 * Mobile and Tablet Optimizations
 * Responsive breakpoints for optimal historical analytics viewing
 * on all device sizes with touch-friendly interactions and layout adjustments.
 */

/* Tablet and small desktop breakpoint */
@media (max-width: 768px) {
  /* Stack header elements vertically */
  .analytics-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  /* Center control buttons */
  .analytics-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  /* Single column overview grid */
  .overview-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Two column summary cards */
  .analytics-summary {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Single column patterns grid */
  .patterns-grid {
    grid-template-columns: 1fr;
  }

  /* Stack trend chart stats vertically */
  .trend-chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  /* Stack trend stats */
  .trend-chart-stats {
    justify-content: center;
    flex-wrap: wrap;
  }

  /* Stack regression header vertically */
  .regression-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  /* Single column regression metrics */
  .regression-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Stack regression actions */
  .regression-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  /* Two column key metrics */
  .key-metrics-grid {
    grid-template-columns: 1fr;
  }
}

/* Mobile phone breakpoint */
@media (max-width: 480px) {
  /* Single column summary cards */
  .analytics-summary {
    grid-template-columns: 1fr;
  }

  /* Single column regression metrics */
  .regression-metrics {
    grid-template-columns: 1fr;
  }

  /* Stack trend filters vertically */
  .trends-filters {
    flex-direction: column;
    gap: 1rem;
  }

  /* Stack health score container */
  .health-score-container {
    flex-direction: column;
    text-align: center;
  }
}

/* ======================================================================= */
/* RESPONSIVE DESIGN                                                       */
/* ======================================================================= */

@media (max-width: 768px) {
  .analytics-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .analytics-controls {
    flex-wrap: wrap;
  }

  .analytics-summary {
    grid-template-columns: 1fr;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .key-metrics-grid {
    grid-template-columns: 1fr;
  }

  .trends-filters {
    flex-direction: column;
    gap: 0.75rem;
  }

  .patterns-grid {
    grid-template-columns: 1fr;
  }

  .regression-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

/**
 * CodePulse Design System Variables
 *
 * This stylesheet defines the complete design system for the CodePulse application
 * using CSS custom properties (variables) for consistent theming and maintainability.
 * Key features include:
 *
 * version 1.0.0
 *
 * - Comprehensive color palette with semantic naming
 * - Light and dark theme support with automatic system detection
 * - Typography scale with consistent font families and weights
 * - Spacing system based on 4px grid
 * - Shadow system for depth and elevation
 * - Border radius scale for consistent rounded corners
 * - Transition timing for smooth animations
 * - Z-index scale for proper layering
 * - Layout constants for consistent sizing
 * - Accessibility features and high contrast support
 * - Reduced motion preferences
 * - Color blindness accommodation
 */

/* ======================================================================= */
/* ROOT DESIGN TOKENS                                                      */
/* ======================================================================= */

/**
 * Light Theme Variables
 * Default color scheme optimized for readability and accessibility
 * with WCAG AA compliant contrast ratios.
 */
:root {
  /* ======================================================================= */
  /* COLOR SYSTEM - LIGHT THEME                                              */
  /* ======================================================================= */

  /**
   * Primary Colors
   * Main brand colors used for primary actions, links, and emphasis.
   */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #dbeafe;

  /* Secondary and accent colors for variety and hierarchy */
  --color-secondary: #64748b;
  --color-accent: #8b5cf6;
  --color-accent-hover: #7c3aed;

  /**
   * Background Colors
   * Layered background system for depth and visual hierarchy.
   */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-bg-elevated: #ffffff;
  --color-bg-overlay: rgba(0, 0, 0, 0.05);

  /**
   * Text Colors
   * Hierarchical text colors with proper contrast ratios.
   */
  --color-text-primary: #0f172a;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;
  --color-text-inverse: #ffffff;
  --color-text-muted: #94a3b8;

  /**
   * Border Colors
   * Subtle borders for component separation and focus states.
   */
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-focus: #2563eb;

  /**
   * Status Colors
   * Semantic colors for success, warning, error, and info states.
   */
  --color-success: #10b981;
  --color-success-bg: #d1fae5;
  --color-warning: #f59e0b;
  --color-warning-bg: #fef3c7;
  --color-error: #ef4444;
  --color-error-bg: #fee2e2;
  --color-info: #3b82f6;
  --color-info-bg: #dbeafe;

  /* ======================================================================= */
  /* TYPOGRAPHY SYSTEM                                                       */
  /* ======================================================================= */

  /**
   * Font Families
   * Carefully selected font stacks for optimal readability and performance.
   */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /**
   * Font Size Scale
   * Modular scale based on 16px base with consistent ratios.
   */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /**
   * Font Weight Scale
   * Semantic font weights for proper typography hierarchy.
   */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /**
   * Line Height Scale
   * Optimized line heights for different content types.
   */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  /* ======================================================================= */
  /* SPACING SYSTEM                                                          */
  /* ======================================================================= */

  /**
   * Spacing Scale
   * 4px-based spacing system for consistent layout and rhythm.
   */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */

  /* ======================================================================= */
  /* SHADOW SYSTEM                                                           */
  /* ======================================================================= */

  /**
   * Shadow Scale
   * Layered shadow system for depth perception and visual hierarchy.
   * Shadows create elevation and help establish component relationships.
   */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* ======================================================================= */
  /* BORDER RADIUS SYSTEM                                                    */
  /* ======================================================================= */

  /**
   * Border Radius Scale
   * Consistent rounded corner system for modern, friendly interface design.
   */
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* ======================================================================= */
  /* ANIMATION SYSTEM                                                        */
  /* ======================================================================= */

  /**
   * Transition Timing
   * Consistent animation durations for smooth, professional interactions.
   */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
  --transition-slower: 500ms ease-in-out;

  /* ======================================================================= */
  /* Z-INDEX SCALE                                                           */
  /* ======================================================================= */

  /**
   * Z-Index Layering System
   * Organized stacking context for proper element layering and modal management.
   */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* ======================================================================= */
  /* LAYOUT CONSTANTS                                                        */
  /* ======================================================================= */

  /**
   * Layout Dimensions
   * Standard layout measurements for consistent component sizing.
   */
  --container-max-width: 1200px;
  --sidebar-width: 280px;
  --header-height: 64px;
  --footer-height: 48px;

  /**
   * Responsive Breakpoints
   * Breakpoint values exposed for JavaScript usage and consistency.
   */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ======================================================================= */
/* DARK THEME OVERRIDES                                                    */
/* ======================================================================= */

/**
 * Dark Theme Color Scheme
 * Optimized dark theme with proper contrast ratios and reduced eye strain.
 * Maintains visual hierarchy while providing comfortable viewing in low light.
 */
[data-theme="dark"] {
  /**
   * Dark Theme Background Colors
   * Layered dark backgrounds for depth and visual separation.
   */
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-elevated: #1e293b;
  --color-bg-overlay: rgba(255, 255, 255, 0.05);

  /**
   * Dark Theme Text Colors
   * High contrast text colors optimized for dark backgrounds.
   */
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #0f172a;
  --color-text-muted: #64748b;

  /**
   * Dark Theme Border Colors
   * Subtle borders that work well on dark backgrounds.
   */
  --color-border-primary: #334155;
  --color-border-secondary: #475569;

  /**
   * Dark Theme Status Colors
   * Adjusted status color backgrounds for dark theme compatibility.
   */
  --color-success-bg: rgba(16, 185, 129, 0.1);
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-error-bg: rgba(239, 68, 68, 0.1);
  --color-info-bg: rgba(59, 130, 246, 0.1);

  /**
   * Dark Theme Shadows
   * Enhanced shadows for better depth perception on dark backgrounds.
   */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
}

/* ======================================================================= */
/* SYSTEM THEME DETECTION                                                  */
/* ======================================================================= */

/**
 * Automatic Dark Theme
 * Respects user's system preference for dark mode when no explicit
 * theme is set, providing seamless integration with OS settings.
 */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* Apply dark theme variables when system prefers dark and no explicit theme is set */
    --color-bg-primary: #0f172a;
    --color-bg-secondary: #1e293b;
    --color-bg-tertiary: #334155;
    --color-bg-elevated: #1e293b;
    --color-bg-overlay: rgba(255, 255, 255, 0.05);

    --color-text-primary: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-tertiary: #94a3b8;
    --color-text-inverse: #0f172a;
    --color-text-muted: #64748b;

    --color-border-primary: #334155;
    --color-border-secondary: #475569;

    --color-success-bg: rgba(16, 185, 129, 0.1);
    --color-warning-bg: rgba(245, 158, 11, 0.1);
    --color-error-bg: rgba(239, 68, 68, 0.1);
    --color-info-bg: rgba(59, 130, 246, 0.1);

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  }
}

/* ======================================================================= */
/* ACCESSIBILITY FEATURES                                                  */
/* ======================================================================= */

/**
 * Reduced Motion Support
 * Respects user preference for reduced motion by disabling transitions
 * and animations to prevent vestibular disorders and motion sensitivity.
 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: none;
    --transition-base: none;
    --transition-slow: none;
    --transition-slower: none;
  }
}

/* Class-based reduced motion override */
.reduced-motion,
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/**
 * High Contrast Mode
 * Enhanced contrast colors for users with low vision or
 * those who prefer higher contrast for better readability.
 */
.high-contrast {
  --color-primary: #0066cc;
  --color-primary-hover: #004499;
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f0f0f0;
  --color-text-primary: #000000;
  --color-text-secondary: #333333;
  --color-border-primary: #000000;
  --color-border-secondary: #666666;
}

/* Dark theme high contrast variant */
[data-theme="dark"].high-contrast {
  --color-primary: #66b3ff;
  --color-primary-hover: #3399ff;
  --color-bg-primary: #000000;
  --color-bg-secondary: #1a1a1a;
  --color-text-primary: #ffffff;
  --color-text-secondary: #cccccc;
  --color-border-primary: #ffffff;
  --color-border-secondary: #999999;
}

/**
 * Enhanced Focus Indicators
 * High-visibility focus states for keyboard navigation accessibility.
 */
.style-focus *:focus {
  outline: 3px solid var(--color-primary) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 1px var(--color-bg-primary), 0 0 0 4px var(--color-primary) !important;
}

/* Interactive element focus enhancement */
.style-focus button:focus,
.style-focus a:focus,
.style-focus input:focus,
.style-focus select:focus,
.style-focus textarea:focus {
  background-color: var(--color-primary-light) !important;
}

/**
 * Font Size Accessibility Options
 * User-configurable font sizes for different visual needs.
 */
[data-font-size="small"] {
  font-size: 0.875rem;
}

[data-font-size="normal"] {
  font-size: 1rem;
}

[data-font-size="large"] {
  font-size: 1.125rem;
}

[data-font-size="extra-large"] {
  font-size: 1.25rem;
}

/**
 * Color Blindness Support
 * SVG filter applications for different types of color vision deficiency.
 */
[data-colorblind="protanopia"] {
  filter: url('#protanopia-filter');
}

[data-colorblind="deuteranopia"] {
  filter: url('#deuteranopia-filter');
}

[data-colorblind="tritanopia"] {
  filter: url('#tritanopia-filter');
}

/**
 * Screen Reader Optimizations
 * Hidden content that remains accessible to assistive technologies.
 */
.screen-reader-mode .visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.screen-reader-mode .sr-only {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/**
 * Skip Navigation Links
 * Allows keyboard users to bypass repetitive navigation elements.
 */
.skip-links {
  position: absolute;
  top: -40px;
  left: 6px;
  z-index: var(--z-tooltip);
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-text-inverse);
  padding: 8px 16px;
  text-decoration: none;
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
  z-index: var(--z-tooltip);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/**
 * Theme Transition Enhancements
 * Smooth transitions when switching between light and dark themes.
 */
.theme-transitioning,
.theme-transitioning * {
  transition: background-color var(--transition-base),
              color var(--transition-base),
              border-color var(--transition-base),
              box-shadow var(--transition-base) !important;
}

.theme-transitioning .chart-container canvas {
  transition: opacity var(--transition-base);
}

/**
 * System High Contrast Support
 * Automatically adjusts colors when user prefers high contrast.
 */
@media (prefers-contrast: high) {
  :root {
    --color-text-primary: #000000;
    --color-text-secondary: #333333;
    --color-border-primary: #000000;
  }

  [data-theme="dark"] {
    --color-text-primary: #ffffff;
    --color-text-secondary: #cccccc;
    --color-border-primary: #ffffff;
  }
}

/**
 * CodePulse Reporting System Stylesheet
 *
 * This stylesheet provides comprehensive styling for the reporting management interface,
 * enabling users to generate, schedule, and manage various types of reports with an
 * intuitive and professional user experience. Key features include:
 *
 * version 1.0.0
 *
 * - Report generation interface with form controls and validation
 * - Scheduled reports management with CRUD operations
 * - Report history with filtering and search capabilities
 * - Template system for reusable report configurations
 * - Progress tracking for report generation processes
 * - Statistics dashboard for reporting insights
 * - Modal dialogs for detailed interactions
 * - Responsive design for all device sizes
 * - Accessibility-compliant interface elements
 * - Professional styling with smooth animations
 */

/* ======================================================================= */
/* MAIN CONTAINER LAYOUT                                                   */
/* ======================================================================= */

/**
 * Primary Container
 * Main wrapper for the reporting system interface with consistent
 * padding, background, and border radius for professional appearance.
 */

/* Main reporting container */
.reporting-manager-container {
  padding: 1.5rem;
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  margin-bottom: 2rem;
}

/* Header section with title and actions */
.reporting-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border-primary);
}

/* Header title styling */
.reporting-header h2 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

/* Action buttons container */
.reporting-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Icon spacing in buttons */
.btn-icon {
  margin-right: 0.5rem;
}

/* ======================================================================= */
/* STATISTICS DASHBOARD                                                     */
/* ======================================================================= */

/**
 * Statistics Cards
 * Overview cards displaying key reporting metrics and statistics
 * with responsive grid layout and hover effects.
 */

/* Statistics section container */
.reporting-stats {
  margin-bottom: 2rem;
}

/* Responsive grid for statistics cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

/* Individual statistic card */
.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

/* Stat card hover effects */
.stat-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* Icon styling in stat cards */
.stat-icon {
  font-size: 1.75rem;
  flex-shrink: 0;
}

/* Content area of stat card */
.stat-content {
  flex: 1;
  min-width: 0;
}

/* Primary statistic value */
.stat-value {
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

/* Statistic label */
.stat-label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* ======================================================================= */
/* TAB NAVIGATION SYSTEM                                                    */
/* ======================================================================= */

/**
 * Tab Interface
 * Navigation tabs for switching between different reporting sections
 * with active states and smooth transitions.
 */

/* Tab navigation container */
.reporting-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border-primary);
  margin-bottom: 2rem;
  overflow-x: auto;
}

/* Individual tab button */
.reporting-tab {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  border-bottom: 2px solid transparent;
}

/* Tab hover state */
.reporting-tab:hover {
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
}

/* Active tab styling */
.reporting-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background: var(--color-primary-light);
}

/* Hidden tab content */
.reporting-tab-content {
  display: none;
}

/* Active tab content */
.reporting-tab-content.active {
  display: block;
}

/* ======================================================================= */
/* REPORT GENERATOR INTERFACE                                              */
/* ======================================================================= */

/**
 * Report Generator
 * Comprehensive form interface for creating custom reports with
 * various configuration options and real-time validation.
 */

/* Report generator container */
.report-generator {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: 2rem;
}

/* Generator form layout */
.generator-form {
  margin-bottom: 2rem;
}

/* Form section grouping */
.form-section {
  margin-bottom: 2rem;
}

/* Form section headings */
.form-section h3 {
  margin: 0 0 1rem 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* Responsive form grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

/* Individual form group */
.form-group {
  display: flex;
  flex-direction: column;
}

/* Form labels */
.form-group label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.5rem;
}

/* Form input controls */
.form-control {
  padding: 0.75rem;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

/* Form control focus state */
.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Multi-select controls */
.form-control[multiple] {
  min-height: 100px;
}

/* Form action buttons */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-border-secondary);
}

/* ======================================================================= */
/* GENERATION PROGRESS TRACKING                                            */
/* ======================================================================= */

/**
 * Progress Indicators
 * Visual feedback for report generation progress with animated
 * progress bars and status updates.
 */

/* Progress tracking container */
.generation-progress {
  text-align: center;
  padding: 2rem;
}

/* Progress header with title and controls */
.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

/* Progress title */
.progress-header h4 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* Progress bar container */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: 1rem;
}

/* Animated progress fill */
.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
  width: 0%;
}

/* Progress status text */
.progress-status {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* ======================================================================= */
/* SCHEDULED REPORTS MANAGEMENT                                             */
/* ======================================================================= */

/**
 * Scheduled Reports
 * Interface for managing automated report schedules with
 * CRUD operations and status indicators.
 */

/* Scheduled reports container */
.scheduled-reports {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: 2rem;
}

/* Scheduled reports header */
.scheduled-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

/* Scheduled reports title */
.scheduled-header h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* List of scheduled reports */
.scheduled-list {
  margin-bottom: 1rem;
}

.scheduled-list > * + * {
  margin-top: 1rem;
}

/* Individual scheduled report item */
.scheduled-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem;
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
}

/* Scheduled item hover effects */
.scheduled-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* Scheduled report information */
.scheduled-info h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

/* Scheduled report metadata */
.scheduled-meta {
  display: flex;
  gap: 1rem;
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/* Scheduled report action buttons */
.scheduled-actions {
  display: flex;
  gap: 0.5rem;
}

/* ======================================================================= */
/* REPORT HISTORY INTERFACE                                                */
/* ======================================================================= */

/**
 * Report History
 * Historical view of generated reports with filtering,
 * search capabilities, and download options.
 */

/* Report history container */
.report-history {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: 2rem;
}

/* History section header */
.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

/* History section title */
.history-header h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* History filtering controls */
.history-filters {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* Filter control sizing */
.history-filters .form-control {
  min-width: 120px;
  padding: 0.5rem;
}

/* History items list */
.history-list {
  margin-bottom: 1rem;
}

.history-list > * + * {
  margin-top: 1rem;
}

/* Individual history item */
.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem;
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
}

/* History item hover effects */
.history-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* History item information */
.history-info h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

/* History item metadata */
.history-meta {
  display: flex;
  gap: 1rem;
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/* History item action buttons */
.history-actions {
  display: flex;
  gap: 0.5rem;
}

/* ======================================================================= */
/* REPORT TEMPLATES SYSTEM                                                 */
/* ======================================================================= */

/**
 * Report Templates
 * Pre-configured report templates for quick report generation
 * with visual previews and customization options.
 */

/* Report templates container */
.report-templates {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  padding: 2rem;
}

/* Templates section header */
.templates-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

/* Templates section title */
.templates-header h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* Responsive templates grid */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

/* Individual template card */
.template-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: center;
  transition: all var(--transition-fast);
}

/* Template card hover effects */
.template-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* Template preview area */
.template-preview {
  margin-bottom: 1rem;
}

/* Template icon styling */
.template-icon {
  font-size: 3rem;
  color: var(--color-text-tertiary);
}

/* Template information */
.template-info h4 {
  margin: 0 0 0.5rem 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

/* Template type indicator */
.template-type {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 1rem;
}

/* Template action buttons */
.template-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* ======================================================================= */
/* MODAL DIALOG SYSTEM                                                     */
/* ======================================================================= */

/**
 * Modal Dialogs
 * Overlay dialogs for detailed interactions, confirmations,
 * and complex form interfaces with accessibility support.
 */

/* Modal overlay */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Modal content container */
.modal-content {
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

/* Large modal variant */
.modal-content.large {
  max-width: 800px;
}

/* Modal header with title and close button */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border-primary);
}

/* Modal title */
.modal-header h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* Modal close button */
.modal-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
}

/* Modal close button hover state */
.modal-close:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

/* Modal body content */
.modal-body {
  padding: 1.5rem;
}

/* ======================================================================= */
/* EMPTY STATES                                                            */
/* ======================================================================= */

/**
 * Empty State Styling
 * Placeholder content for empty lists and sections
 * with helpful messaging and visual cues.
 */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-sm);
}

/* ======================================================================= */
/* RESPONSIVE LAYOUTS                                                      */
/* ======================================================================= */

/**
 * Mobile and Tablet Optimizations
 * Responsive breakpoints for optimal viewing on all device sizes
 * with touch-friendly interactions and layout adjustments.
 */

/* Tablet and small desktop breakpoint */
@media (max-width: 768px) {
  /* Stack header elements vertically */
  .reporting-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  /* Center action buttons */
  .reporting-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  /* Reduce stats grid to 2 columns */
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Single column form layout */
  .form-grid {
    grid-template-columns: 1fr;
  }

  /* Stack form actions vertically */
  .form-actions {
    flex-direction: column;
  }

  /* Stack list items vertically */
  .scheduled-item,
  .history-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  /* Center action buttons in list items */
  .scheduled-actions,
  .history-actions {
    justify-content: center;
  }

  /* Stack filter controls vertically */
  .history-filters {
    flex-direction: column;
    align-items: stretch;
  }

  /* Single column template grid */
  .templates-grid {
    grid-template-columns: 1fr;
  }

  /* Full-width modal on mobile */
  .modal-content {
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}

/* Mobile phone breakpoint */
@media (max-width: 480px) {
  /* Single column stats grid */
  .stats-grid {
    grid-template-columns: 1fr;
  }

  /* Stack metadata vertically */
  .scheduled-meta,
  .history-meta {
    flex-direction: column;
    gap: 0.25rem;
  }

  /* Stack template actions vertically */
  .template-actions {
    flex-direction: column;
  }
}

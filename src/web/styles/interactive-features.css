/**
 * CodePulse Interactive Features Stylesheet
 *
 * This stylesheet implements a comprehensive set of interactive features for the CodePulse
 * application, enhancing user engagement and providing advanced data exploration capabilities.
 * Key features include:
 *
 * version 1.0.0
 *
 * - Drill-down modals for detailed insights and analysis
 * - Advanced filter panel with complex filtering capabilities
 * - Search highlights with visual ranking and navigation
 * - Export modal with format selection and preview
 * - Goal tracking panel with progress monitoring
 * - Sorting and filtering for tabular data
 * - Smooth animations and micro-interactions
 * - Form validation with real-time feedback
 * - Accessibility-compliant interactive elements
 * - Responsive design for all device sizes
 * - Professional styling with smooth transitions
 * - Touch-friendly interface optimizations
 */


/* ======================================================================= */
/* DRILL-DOWN MODAL                                                        */
/* ======================================================================= */

/**
 * Drill-Down Modal System
 * Interactive modal for detailed data exploration and analysis with
 * responsive design, smooth animations, and accessibility features.
 */

/* Modal overlay with backdrop */
.drill-down-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-base), visibility var(--transition-base);
}

/* Modal visible state */
.drill-down-modal.show {
  opacity: 1;
  visibility: visible;
}

/* Modal content container */
.drill-down-content {
  background: var(--color-bg-elevated);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform var(--transition-base);
}

/* Modal content animation when shown */
.drill-down-modal.show .drill-down-content {
  transform: scale(1);
}

/* Modal header with title and close button */
.drill-down-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border-primary);
}

/* Modal title styling */
.drill-down-header h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

/* Close button styling */
.close-drill-down {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-base);
  transition: background-color var(--transition-fast);
}

/* Close button hover state */
.close-drill-down:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

/* Modal body with scrollable content */
.drill-down-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* Summary metrics grid */
.drill-down-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Individual summary metric item */
.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

/* Summary metric label */
.summary-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Summary metric value */
.summary-value {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-semibold);
}

/* Chart container within modal */
.drill-down-chart-container {
  height: 300px;
  margin-bottom: 1.5rem;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-base);
  padding: 1rem;
}

/* Modal action buttons */
.drill-down-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

/* ======================================================================= */
/* ADVANCED FILTER PANEL                                                   */
/* ======================================================================= */

/**
 * Advanced Filter Panel System
 * Comprehensive sidebar panel for advanced filtering and search capabilities
 * with complex filter builders, saved filters, and responsive design.
 */

/* Filter panel container */
.advanced-filter-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--color-bg-elevated);
  border-left: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-sidebar);
  transition: right var(--transition-base);
  overflow-y: auto;
}

/* Filter panel visible state */
.advanced-filter-panel.show {
  right: 0;
}

/* Filter panel header with sticky positioning */
.filter-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border-primary);
  background: var(--color-bg-primary);
  position: sticky;
  top: 0;
  z-index: 1;
}

/* Filter panel title */
.filter-panel-header h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

/* Close filter panel button */
.close-filter-panel {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-base);
  transition: background-color var(--transition-fast);
}

/* Close button hover state */
.close-filter-panel:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

/* Filter panel body content */
.filter-panel-body {
  padding: 1.5rem;
}

/**
 * Filter Controls
 * Individual filter input components with various types
 * including text, select, date range, checkbox, and radio groups.
 */

/* Filter group container */
.filter-group {
  margin-bottom: 1.5rem;
}

/* Filter labels */
.filter-label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

/* Text and select filter inputs */
.filter-input,
.filter-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-fast);
}

/* Filter input focus states */
.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Date range input container */
.date-range-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Date range separator text */
.date-range-inputs span {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Checkbox group container */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Checkbox labels with icons */
.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
}

/* Radio button group container */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Radio button labels */
.radio-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
}

/* Range slider container */
.range-inputs {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Range slider input */
.filter-range {
  width: 100%;
}

/* Range value display */
.range-values {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/**
 * Custom Filter Builder
 * Advanced filter construction interface with dynamic filter creation
 * and management capabilities.
 */

/* Custom filter builder grid */
.custom-filter-builder {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto;
  gap: 0.5rem;
  align-items: end;
}

/* List of custom filters */
.custom-filters-list {
  margin-top: 0.75rem;
}

/* Individual custom filter item */
.custom-filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-base);
  margin-bottom: 0.5rem;
  font-size: var(--font-size-sm);
}

/* Filter panel footer with actions */
.filter-panel-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--color-border-primary);
  background: var(--color-bg-primary);
}

/* Filter action buttons */
.filter-actions {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

/* Saved filters dropdown */
.saved-filters {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-sm);
}

/* Saved filters label */
.saved-filters label {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Saved filters select dropdown */
.saved-filters select {
  flex: 1;
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-xs);
}

/* ======================================================================= */
/* SEARCH HIGHLIGHTS SYSTEM                                                */
/* ======================================================================= */

/**
 * Search Highlights
 * Visual highlighting system for search results with ranking indicators,
 * animations, and result count display for enhanced search experience.
 */

/* Search highlight container with animation */
.search-highlight {
  position: relative;
  background: rgba(255, 235, 59, 0.2);
  border: 2px solid #ffeb3b;
  border-radius: var(--radius-base);
  animation: searchPulse 2s ease-in-out;
}

/* Search rank indicator badge */
.search-highlight::before {
  content: attr(data-search-rank);
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--color-primary);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

/* Search highlight pulse animation */
@keyframes searchPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

/* Search results count indicator */
.search-results-count {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--color-primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-top: 0.25rem;
}

/* ======================================================================= */
/* EXPORT MODAL                                                            */
/* ======================================================================= */

/**
 * Export Modal System
 * Comprehensive data export interface with format selection, configuration
 * options, preview capabilities, and progress tracking for various export formats.
 */

/* Export modal overlay */
.export-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-base), visibility var(--transition-base);
}

/* Export modal visible state */
.export-modal.show {
  opacity: 1;
  visibility: visible;
}

/* Export modal content container */
.export-modal-content {
  background: var(--color-bg-elevated);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  width: 700px;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform var(--transition-base);
}

/* Export modal content animation */
.export-modal.show .export-modal-content {
  transform: scale(1);
}

/* Export modal header */
.export-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border-primary);
}

/* Export modal body with scrollable content */
.export-modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

/**
 * Export Format Selection
 * Visual format selection interface with icons and interactive
 * radio button styling for different export formats.
 */

/* Section headings */
.export-format-selection h4,
.export-options h4 {
  margin: 0 0 1rem 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* Format options grid */
.format-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  margin-bottom: 2rem;
}

/* Individual format option card */
.format-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--color-bg-primary);
}

/* Format option hover state */
.format-option:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

/* Hidden radio button */
.format-option input[type="radio"] {
  display: none;
}

/* Selected format styling for adjacent elements */
.format-option input[type="radio"]:checked + .format-icon + .format-name {
  color: var(--color-primary);
}

/* Selected format styling for all child elements */
.format-option input[type="radio"]:checked ~ * {
  color: var(--color-primary);
}

/* Selected format option container styling */
.format-option:has(input[type="radio"]:checked) {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

/* Format icon styling */
.format-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* Format name text */
.format-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: center;
}

/**
 * Export Options Configuration
 * Form controls for configuring export parameters including
 * date ranges, format-specific options, and preview generation.
 */

/* Option group container */
.option-group {
  margin-bottom: 1.5rem;
}

/* Option labels */
.option-label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

/* Export form controls */
.export-select,
.export-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  background: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
}

/* Date range input container */
.date-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Date range separator */
.date-inputs span {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/**
 * Export Preview System
 * Live preview of export output with syntax highlighting
 * and structured content display.
 */

/* Export preview section */
.export-preview {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-border-primary);
}

/* Preview content container */
.preview-container {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-base);
  padding: 1rem;
  min-height: 150px;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

/* Preview placeholder text */
.preview-placeholder {
  color: var(--color-text-tertiary);
  text-align: center;
  padding: 2rem;
}

/* Preview header styling */
.preview-header {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

/* Preview section content */
.preview-section {
  color: var(--color-text-secondary);
  margin: 0.25rem 0;
}

/* Preview footer information */
.preview-footer {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid var(--color-border-secondary);
}

/**
 * Export Modal Footer
 * Footer section with progress tracking and action buttons
 * for export operations with animated progress indicators.
 */

/* Export modal footer */
.export-modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--color-border-primary);
  background: var(--color-bg-primary);
}

/* Export progress section */
.export-progress {
  margin-bottom: 1rem;
}

/* Progress bar container */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

/* Animated progress fill */
.progress-fill {
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
  animation: progressPulse 2s ease-in-out infinite;
}

/* Progress pulse animation */
@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Progress status text */
.progress-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: 0.5rem;
}

/* Export action buttons */
.export-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

/* ======================================================================= */
/* GOAL TRACKING PANEL SYSTEM                                              */
/* ======================================================================= */

/**
 * Goal Tracking Panel
 * Floating panel for goal management and achievement tracking with
 * collapsible interface, tabbed content, and progress visualization.
 */

/* Goal tracking panel container */
.goal-panel {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  width: 350px;
  max-height: 500px;
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-popover);
  transition: transform var(--transition-base);
}

/* Collapsed goal panel state */
.goal-panel.collapsed {
  transform: translateY(calc(100% - 60px));
}

/* Goal panel header with controls */
.goal-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--color-border-primary);
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

/* Goal panel title */
.goal-panel-header h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

/* Goal panel action buttons */
.goal-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Panel collapse/expand toggle */
.goal-panel-toggle {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-base);
  transition: background-color var(--transition-fast);
}

/* Toggle button hover state */
.goal-panel-toggle:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

/* Goal panel body with scrollable content */
.goal-panel-body {
  max-height: 400px;
  overflow-y: auto;
}

/**
 * Goal Panel Tabs
 * Tab navigation system for switching between goals and achievements
 * with active states and smooth transitions.
 */

/* Goal tabs container */
.goal-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border-primary);
}

/* Individual goal tab */
.goal-tab {
  flex: 1;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

/* Active goal tab */
.goal-tab.active {
  color: var(--color-primary);
  background: var(--color-primary-light);
}

/* Goal tab hover state (excluding active) */
.goal-tab:hover:not(.active) {
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
}

/* Hidden tab content */
.goal-tab-content {
  display: none;
  padding: 1rem;
}

/* Active tab content */
.goal-tab-content.active {
  display: block;
}

/**
 * Goal Lists
 * Container for goal and achievement items with consistent
 * spacing and layout structure.
 */

/* Goals and achievements list containers */
.goals-list,
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/**
 * Goal Cards
 * Individual goal display cards with priority indicators,
 * progress tracking, and interactive elements.
 */

/* Base goal card styling */
.goal-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  padding: 1rem;
  transition: all var(--transition-fast);
}

/* Goal card hover effects */
.goal-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* High priority goal indicator */
.goal-card.high {
  border-left: 4px solid #ef4444;
}

/* Medium priority goal indicator */
.goal-card.medium {
  border-left: 4px solid #f59e0b;
}

/* Low priority goal indicator */
.goal-card.low {
  border-left: 4px solid #10b981;
}

/* Overdue goal styling */
.goal-card.overdue {
  background: rgba(239, 68, 68, 0.05);
  border-color: #ef4444;
}

/* Goal card header with icon and actions */
.goal-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

/* Goal icon styling */
.goal-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

/* Goal information container */
.goal-info {
  flex: 1;
  min-width: 0;
}

/* Goal title */
.goal-title {
  margin: 0 0 0.25rem 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  line-height: 1.3;
}

/* Goal description */
.goal-description {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  line-height: 1.4;
}

/* Goal action buttons container */
.goal-actions {
  display: flex;
  gap: 0.25rem;
}

/* Individual goal action button */
.goal-action {
  background: none;
  border: none;
  padding: 0.25rem;
  border-radius: var(--radius-base);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: background-color var(--transition-fast);
}

/* Goal action button hover state */
.goal-action:hover {
  background: var(--color-bg-secondary);
}

/**
 * Goal Progress Tracking
 * Visual progress indicators with animated progress bars
 * and percentage display for goal completion tracking.
 */

/* Goal progress section */
.goal-progress {
  margin-bottom: 0.75rem;
}

/* Progress bar container */
.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: 0.25rem;
}

/* Animated progress fill */
.progress-fill {
  height: 100%;
  background: var(--color-primary);
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
}

/* Progress percentage text */
.progress-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  text-align: center;
}

/**
 * Goal Metadata
 * Badges and indicators for goal properties including
 * priority levels, deadlines, and time periods.
 */

/* Goal metadata container */
.goal-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-xs);
}

/* Base metadata badge styling */
.goal-period,
.goal-priority,
.goal-deadline {
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
}

/* Goal period badge */
.goal-period {
  background: var(--color-bg-secondary);
  color: var(--color-text-secondary);
}

/* High priority badge */
.priority-high {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Medium priority badge */
.priority-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

/* Low priority badge */
.priority-low {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

/* Goal deadline badge */
.goal-deadline {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

/* Overdue deadline badge */
.goal-deadline.overdue {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/**
 * Achievement Cards
 * Display cards for completed achievements with icons,
 * descriptions, and completion dates.
 */

/* Achievement card container */
.achievement-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-base);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Achievement icon */
.achievement-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

/* Achievement information container */
.achievement-info {
  flex: 1;
  min-width: 0;
}

/* Achievement title */
.achievement-title {
  margin: 0 0 0.25rem 0;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

/* Achievement description */
.achievement-description {
  margin: 0 0 0.25rem 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
}

/* Achievement completion date */
.achievement-date {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-xs);
}

/**
 * Empty State
 * Placeholder content for empty goal and achievement lists
 * with helpful messaging and visual cues.
 */
.empty-state {
  text-align: center;
  color: var(--color-text-tertiary);
  font-size: var(--font-size-sm);
  padding: 2rem 1rem;
}

/* ======================================================================= */
/* RESPONSIVE DESIGN                                                       */
/* ======================================================================= */

/**
 * Mobile and Tablet Optimizations
 * Responsive breakpoints for optimal interactive feature viewing
 * on all device sizes with touch-friendly adaptations.
 */

/* Tablet and mobile breakpoint */
@media (max-width: 768px) {
  /* Full-width filter panel on mobile */
  .advanced-filter-panel {
    width: 100vw;
    right: -100vw;
  }

  /* Responsive modal sizing */
  .drill-down-content,
  .export-modal-content {
    width: 95vw;
    margin: 1rem;
  }

  /* Full-width goal panel on mobile */
  .goal-panel {
    width: calc(100vw - 2rem);
    right: 1rem;
    left: 1rem;
  }

  /* Two-column format options on mobile */
  .format-options {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Single-column filter builder on mobile */
  .custom-filter-builder {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

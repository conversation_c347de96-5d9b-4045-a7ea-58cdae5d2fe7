<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test - Code Pulse</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1e1e1e;
            color: #ffffff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status-panel {
            background: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected { background: #4CAF50; }
        .status-connecting { background: #FF9800; }
        .status-disconnected { background: #f44336; }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        button {
            background: #007ACC;
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #005a9e;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .log-panel {
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-timestamp {
            color: #888;
            margin-right: 10px;
        }
        
        .log-type-info { color: #4CAF50; }
        .log-type-warning { color: #FF9800; }
        .log-type-error { color: #f44336; }
        .log-type-debug { color: #2196F3; }
        
        .metrics-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .metric-card {
            background: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WebSocket Test - Code Pulse</h1>
            <p>Test WebSocket connection and real-time data updates</p>
        </div>
        
        <div class="status-panel">
            <h3>Connection Status</h3>
            <div id="connectionStatus">
                <span class="status-indicator status-disconnected"></span>
                <span id="statusText">Disconnected</span>
            </div>
            <div style="margin-top: 10px;">
                <small>Port: <span id="portInfo">Unknown</span></small><br>
                <small>Updates Received: <span id="updateCount">0</span></small><br>
                <small>Last Update: <span id="lastUpdate">Never</span></small>
            </div>
        </div>
        
        <div class="controls">
            <button id="connectBtn">Connect</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
            <button id="requestMetricsBtn" disabled>Request Metrics</button>
            <button id="requestInsightsBtn" disabled>Request Insights</button>
            <button id="clearLogBtn">Clear Log</button>
        </div>
        
        <div class="metrics-panel">
            <div class="metric-card">
                <div class="metric-value" id="metricDuration">0.0</div>
                <div class="metric-label">Duration (min)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="metricActiveTime">0.0</div>
                <div class="metric-label">Active Time (min)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="metricFilesTracked">0</div>
                <div class="metric-label">Files Tracked</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="metricComplexity">0.0</div>
                <div class="metric-label">Avg Complexity</div>
            </div>
        </div>
        
        <div class="status-panel">
            <h3>Event Log</h3>
            <div id="logPanel" class="log-panel"></div>
        </div>
    </div>

    <script>
        class WebSocketTester {
            constructor() {
                this.socket = null;
                this.isConnected = false;
                this.updateCount = 0;
                this.port = 8080; // Default port for testing
                
                this.initializeUI();
                this.setupEventListeners();
            }
            
            initializeUI() {
                this.statusIndicator = document.querySelector('.status-indicator');
                this.statusText = document.getElementById('statusText');
                this.portInfo = document.getElementById('portInfo');
                this.updateCountEl = document.getElementById('updateCount');
                this.lastUpdateEl = document.getElementById('lastUpdate');
                this.logPanel = document.getElementById('logPanel');
                
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.requestMetricsBtn = document.getElementById('requestMetricsBtn');
                this.requestInsightsBtn = document.getElementById('requestInsightsBtn');
                this.clearLogBtn = document.getElementById('clearLogBtn');
                
                // Try to get port from URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const portParam = urlParams.get('port');
                if (portParam) {
                    this.port = parseInt(portParam);
                }
                
                this.portInfo.textContent = this.port;
            }
            
            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                this.requestMetricsBtn.addEventListener('click', () => this.requestMetrics());
                this.requestInsightsBtn.addEventListener('click', () => this.requestInsights());
                this.clearLogBtn.addEventListener('click', () => this.clearLog());
            }
            
            connect() {
                if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                    this.log('Already connected', 'warning');
                    return;
                }
                
                this.updateStatus('connecting', 'Connecting...');
                this.log(`Connecting to ws://localhost:${this.port}`, 'info');
                
                try {
                    this.socket = new WebSocket(`ws://localhost:${this.port}`);
                    this.setupSocketHandlers();
                } catch (error) {
                    this.log(`Connection error: ${error.message}`, 'error');
                    this.updateStatus('disconnected', 'Connection Failed');
                }
            }
            
            disconnect() {
                if (this.socket) {
                    this.socket.close(1000, 'User requested disconnect');
                }
            }
            
            setupSocketHandlers() {
                this.socket.onopen = () => {
                    this.isConnected = true;
                    this.updateStatus('connected', 'Connected');
                    this.log('WebSocket connected successfully', 'info');
                    this.updateButtons();
                };
                
                this.socket.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        this.handleMessage(message);
                    } catch (error) {
                        this.log(`Error parsing message: ${error.message}`, 'error');
                    }
                };
                
                this.socket.onclose = (event) => {
                    this.isConnected = false;
                    this.updateStatus('disconnected', 'Disconnected');
                    this.log(`Connection closed: ${event.code} - ${event.reason}`, 'warning');
                    this.updateButtons();
                };
                
                this.socket.onerror = (error) => {
                    this.log(`WebSocket error: ${error}`, 'error');
                };
            }
            
            handleMessage(message) {
                this.updateCount++;
                this.updateCountEl.textContent = this.updateCount;
                this.lastUpdateEl.textContent = new Date().toLocaleTimeString();
                
                this.log(`Received: ${message.command}`, 'debug');
                
                switch (message.command) {
                    case 'metricsUpdate':
                        this.updateMetrics(message.data);
                        break;
                    case 'insightsUpdate':
                        this.log('Insights update received', 'info');
                        break;
                    case 'connected':
                        this.log(`Server confirmed connection: ${message.data.clientId}`, 'info');
                        break;
                    case 'pong':
                        this.log('Pong received', 'debug');
                        break;
                    default:
                        this.log(`Unknown command: ${message.command}`, 'warning');
                }
            }
            
            updateMetrics(metrics) {
                if (metrics) {
                    document.getElementById('metricDuration').textContent = 
                        (metrics.duration || 0).toFixed(1);
                    document.getElementById('metricActiveTime').textContent = 
                        (metrics.activeTime || 0).toFixed(1);
                    document.getElementById('metricFilesTracked').textContent = 
                        metrics.filesTracked || 0;
                    document.getElementById('metricComplexity').textContent = 
                        (metrics.averageComplexity || 0).toFixed(1);
                }
            }
            
            requestMetrics() {
                this.sendMessage({
                    command: 'requestMetrics'
                });
            }
            
            requestInsights() {
                this.sendMessage({
                    command: 'requestInsights'
                });
            }
            
            sendMessage(message) {
                if (this.isConnected && this.socket.readyState === WebSocket.OPEN) {
                    this.socket.send(JSON.stringify({
                        ...message,
                        timestamp: Date.now()
                    }));
                    this.log(`Sent: ${message.command}`, 'debug');
                } else {
                    this.log('Cannot send message: not connected', 'error');
                }
            }
            
            updateStatus(status, text) {
                this.statusIndicator.className = `status-indicator status-${status}`;
                this.statusText.textContent = text;
            }
            
            updateButtons() {
                this.connectBtn.disabled = this.isConnected;
                this.disconnectBtn.disabled = !this.isConnected;
                this.requestMetricsBtn.disabled = !this.isConnected;
                this.requestInsightsBtn.disabled = !this.isConnected;
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.innerHTML = `
                    <span class="log-timestamp">[${timestamp}]</span>
                    <span class="log-type-${type}">[${type.toUpperCase()}]</span>
                    ${message}
                `;
                
                this.logPanel.appendChild(logEntry);
                this.logPanel.scrollTop = this.logPanel.scrollHeight;
            }
            
            clearLog() {
                this.logPanel.innerHTML = '';
            }
        }
        
        // Initialize tester when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.webSocketTester = new WebSocketTester();
        });
    </script>
</body>
</html>

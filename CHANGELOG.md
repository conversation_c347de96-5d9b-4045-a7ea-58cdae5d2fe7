# Changelog

---------------------------------------------------------------------------------------

## [1.0.3] - 2025-04-25

### Added

- Implemented worker thread pool for parallel processing
- Added IndexedDB support for large datasets
- Enhanced cache compression with metrics
- Added OS-aware CPU core detection
- Implemented progressive enhancement for worker threads
- Implemented more analyzers

### Changed

- Improved worker thread management system
- Enhanced cache compression efficiency
- Updated memory management for large files
- Optimized thread pool resource usage
- Changed project structure
- Made Code Pulse twice faster on the startup and showing the Dashboard
- Changed extension logo
- Made the project more modular

### Fixed

- Comment Ratio too high
- Code Complexity Trend file names instead of full path
- Fixed cache compression error handling
- Improved worker message type safety

---------------------------------------------------------------------------------------

## [1.0.2] - 2025-03-29

### Added

- Restored and enhanced test framework
- Worker thread implementation for heavy computations
- Performance optimization for large codebases

### Changed

- Improved error handling system
- Enhanced cache compression
- Updated visualization components

### Removed

- 

### Fixed

- Test suite restoration
- Worker thread stability
- Cache system performance

---------------------------------------------------------------------------------------

## [1.0.1] - 2025-03-08

### Added

- Added missing packages.
- Added missing commands.
- Added more commands.
- Added Data Flow Diagram documentation.
- Added Project architecture documentation.

### Changed

- Changed all the files to ensure proper documentation & best practises of typescript.
- Changed gitignore to ignore uploading unecessary files to github.
- Made the dashboard a bit more responsive and fancier.

### Removed

- Temporary removal of extension.test.ts.

### Fixed

- Fixed esbuild issues.
- Fixed package.json missing peerDependecies.
- Fixed conflict between config files.
- Fixed not tracking files.
- Fixed command not found.

---------------------------------------------------------------------------------------

## [1.0.0-alpha] - 2025-03-05

### Added

- Caching system.
- Complexity Alerts.
- Tracker debounce system.
- Extension Tests.
- Export of metrics option.
- Error handling system.

### Changed

- Temporary-test storage -> Persistant Storage.
- Chart.js is now way fancier, modern and responsive.
- Changed the logic of pretty much all the existing files to something that has more quality, performance & it's scalable.
- Changed tsconfig.json.

### Removed

- Nothing removed.

### Fixed

- Fixed esbuild.js not building properly.
- Fixed esbuild.js missing some `baseconfig` fields.
- Updated all npm dependecies to their most recent versions.
- Fixed npm outdated dependencies.
- Fixed package.json missing node requirement in `"engine"` property.

---------------------------------------------------------------------------------------

## [Preview 1.0-a] - 2025-03-04

### Added

- Initial Commit
- Project Structure design
- Basic logic

---------------------------------------------------------------------------------------

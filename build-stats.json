{"inputs": ["src/metrics/storage.ts", "src/utils/error-handler.ts", "src/metrics/analyzers/typescript-analyzer.ts", "src/metrics/analyzers/python-analyzer.ts", "src/metrics/complexity.ts", "src/utils/config.ts", "src/workers/worker-manager.ts", "src/utils/cache.ts", "src/views/charts.ts", "src/metrics/tracker.ts", "src/views/dashboard.ts", "src/utils/config-export.ts", "src/services/websocket-server.ts", "src/services/websocket-manager.ts", "src/extension.ts"], "outputs": ["dist/extension.js.map", "dist/extension.js"]}
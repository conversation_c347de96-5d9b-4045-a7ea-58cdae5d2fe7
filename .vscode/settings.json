{
    "files.exclude": {
        "out": true,
        "dist": true,
        ".nyc_output": true,
        "coverage": true,
        "node_modules": true,
        "package-lock.json": true,

    },
    "search.exclude": {
        "out": true,
        "dist": true,
        ".nyc_output": true,
        "coverage": true,
        "node_modules": true,
        "package-lock.json": true,
    },
    "files.watcherExclude": {
        "**/node_modules/**": true,
        "**/dist/**": true,
        "**/coverage/**": true
    },
    "typescript.tsc.autoDetect": "off"
}